from typing import Any
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.orm import Session

from app.core.deps import get_db
from app.dao.coupon import cash_coupon_dao, full_reduction_coupon_dao
from app.dao.coupon import coupon_dao, coupon_usage_record_dao, discount_coupon_dao, coupon_batch_dao
from app.models.coupon import CouponType
from app.service.coupon import CouponService


def serialize_coupon_batch(batch):
    """序列化优惠券批次对象为字典"""
    if not batch:
        return None

    try:
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "start_time": batch.start_time.isoformat() if batch.start_time else None,
            "end_time": batch.end_time.isoformat() if batch.end_time else None,
            "valid_duration": batch.valid_duration or 24,
            "coupon_id": batch.coupon_id,
            "status": batch.status.value if batch.status else 0,
            "distribution_channels": batch.distribution_channels or [],
            "distribution_quantity": batch.distribution_quantity or 0,
            "distribution_cycle": batch.distribution_cycle.value if batch.distribution_cycle else None,
            "receive_quantity": batch.receive_quantity or 0,
            "receive_cycle": batch.receive_cycle.value if batch.receive_cycle else None,
            "receive_start_time": batch.receive_start_time.isoformat() if batch.receive_start_time else None,
            "receive_end_time": batch.receive_end_time.isoformat() if batch.receive_end_time else None,
            "created_at": batch.created_at.isoformat() if batch.created_at else None,
            "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
        }
    except Exception as e:
        print(f"Error serializing batch: {e}")
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "coupon_id": batch.coupon_id,
            "status": 1
        }

def serialize_coupon_batch_detail(batch):
    """序列化优惠券批次对象为字典"""
    if not batch:
        return None

    try:
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "start_time": batch.start_time.isoformat() if batch.start_time else None,
            "end_time": batch.end_time.isoformat() if batch.end_time else None,
            "valid_duration": batch.valid_duration or 0,
            "coupon_id": batch.coupon_id,

            "distribution_channels": batch.distribution_channels or [],
            "distribution_cycle": batch.distribution_cycle or None,
            "distribution_quantity": batch.distribution_quantity or 0,

            "receive_cycle": batch.receive_cycle or None,
            "receive_quantity": batch.receive_quantity or 0,
            "receive_start_time": batch.receive_start_time.isoformat() if batch.receive_start_time else None,
            "receive_end_time": batch.receive_end_time.isoformat() if batch.receive_end_time else None,

            "status": batch.status.value if batch.status else 0,
            "created_at": batch.created_at.isoformat() if batch.created_at else None,
            "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
        }
    except Exception as e:
        print(f"Error serializing batch: {e}")
        return {
            "id": batch.id,
            "name": batch.name or "",
            "description": batch.description or "",
            "batch_number": batch.batch_number or 0,
            "quantity": batch.quantity or 0,
            "coupon_id": batch.coupon_id,
            "status": 1
        }

from app.schemas.common import CommonResponse
from app.schemas.coupon import (
    CouponUpdate, CouponSearch, CouponListResponse,
    DiscountCouponCreate, CashCouponCreate, FullReductionCouponCreate, CouponUsageRecordCreate, CouponListData,
    CouponResponse, FullReductionCouponResponse, CouponUsageRecordResponse, CouponStatusUpdateRequest,
    CouponUsageRecordListResponse, CouponUsageRecordSearch, CouponNameSearchResponse,
    CouponUsageRecordBatchCreate, CouponUsageRecordBatchResponse
)
from app.schemas.coupon import DiscountCouponResponse, CashCouponResponse

router = APIRouter()


@router.post("/create/", response_model=CommonResponse)
def create_coupon(
        *,
        db: Session = Depends(get_db),
        body: dict = Body(...),
) -> Any:
    """创建优惠券"""
    try:
        coupon_type = body.get("type", None)
        if coupon_type is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "缺少优惠券类型",
                    "data": None
                }
            )

        # 根据优惠券类型创建对应的优惠券
        if coupon_type == "discount":
            # 创建折扣券
            coupon_model = DiscountCouponCreate(**body)
            db_result = discount_coupon_dao.create(db, coupon_model)
            result = DiscountCouponResponse.model_validate(db_result)
        elif coupon_type == "cash":
            # 创建现金券
            coupon_model = CashCouponCreate(**body)
            db_result = cash_coupon_dao.create(db, coupon_model)
            result = CashCouponResponse.model_validate(db_result)
        elif coupon_type == "full_reduction":
            # 创建满减券
            coupon_model = FullReductionCouponCreate(**body)
            db_result = full_reduction_coupon_dao.create(db, coupon_model)
            result = FullReductionCouponResponse.model_validate(db_result)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": f"不支持的优惠券创建类型: {str(coupon_type)}",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": f"创建{coupon_type}优惠券成功",
            "data": result.model_dump()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建优惠券失败: {str(e)}",
                "data": None
            }
        )


@router.get("/view/{coupon_id}", response_model=CommonResponse)
def get_coupon(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
) -> Any:
    """获取优惠券详情"""
    try:
        # 先获取基础优惠券信息以确定类型
        base_coupon = coupon_dao.get(db, coupon_id)
        if not base_coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券不存在",
                    "data": None
                }
            )

        # 根据优惠券类型使用对应的DAO获取完整信息
        if base_coupon.type == CouponType.DISCOUNT:
            coupon = discount_coupon_dao.get(db, coupon_id)
            result = DiscountCouponResponse.model_validate(coupon)
        elif base_coupon.type == CouponType.CASH:
            coupon = cash_coupon_dao.get(db, coupon_id)
            result = CashCouponResponse.model_validate(coupon)
        elif base_coupon.type == CouponType.FULL_REDUCTION:
            coupon = full_reduction_coupon_dao.get(db, coupon_id)
            result = FullReductionCouponResponse.model_validate(coupon)
        else:
            coupon = base_coupon
            result = CouponResponse.model_validate(coupon)

        return {
            "code": 200,
            "message": f"获取{base_coupon.type.value}优惠券详情成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取优惠券详情失败: {str(e)}",
                "data": None
            }
        )


@router.put("/update/{coupon_id}", response_model=CommonResponse)
def update_coupon(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
        body: dict = Body(...),
) -> Any:
    """更新优惠券"""
    try:
        # 先获取优惠券基础信息
        coupon = coupon_dao.get(db, coupon_id)
        if not coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券不存在",
                    "data": None
                }
            )

        # 根据优惠券类型更新信息
        if coupon.type == "discount":
            coupon_data = DiscountCouponCreate(**body)
            db_result = discount_coupon_dao.update(db, coupon_id, coupon_data)
            result = DiscountCouponResponse.model_validate(db_result)
        elif coupon.type == "cash":
            coupon_data = CashCouponCreate(**body)
            db_result = cash_coupon_dao.update(db, coupon_id, coupon_data)
            result = CashCouponResponse.model_validate(db_result)
        elif coupon.type == "full_reduction":
            coupon_data = FullReductionCouponCreate(**body)
            db_result = full_reduction_coupon_dao.update(db, coupon_id, coupon_data)
            result = FullReductionCouponResponse.model_validate(db_result)
        else:
            coupon_data = CouponUpdate(**body)
            db_result = coupon_dao.update(db, coupon_id, coupon_data)
            result = CouponResponse.model_validate(db_result)

        return {
            "code": 200,
            "message": f"更新{coupon.type}优惠券成功",
            "data": result.model_dump()
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新优惠券失败: {str(e)}",
                "data": None
            }
        )


@router.delete("/delete/{coupon_id}", response_model=CommonResponse)
def delete_coupon(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
) -> Any:
    """删除优惠券"""
    result = coupon_dao.delete(db, coupon_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "code": 404,
                "message": "优惠券不存在",
                "data": None
            }
        )
    return {
        "code": 200,
        "message": "删除成功",
        "data": None
    }


@router.post("/status/{coupon_id}", response_model=CommonResponse)
def update_coupon_status(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
        request: CouponStatusUpdateRequest,
) -> Any:
    """修改优惠券状态"""
    coupon = coupon_dao.update_status(db, coupon_id, request.status)
    if not coupon:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "code": 404,
                "message": "优惠券不存在",
                "data": None
            }
        )

    coupon_model = CouponResponse.model_validate(coupon)
    return {
        "code": 200,
        "message": "优惠券状态更新成功",
        "data": coupon_model.model_dump()
    }


@router.post("/search/", response_model=CouponListResponse)
def search_coupons(
        *,
        db: Session = Depends(get_db),
        search: CouponSearch,
) -> Any:
    """搜索优惠券"""
    skip = (search.page - 1) * search.pageSize
    result = coupon_dao.search(
        session=db,
        keyword=search.keyword,
        status=search.status,
        coupon_type=search.type,
        skip=skip,
        limit=search.pageSize
    )
    return {
        "code": 200,
        "message": "搜索成功",
        "data": CouponListData(
            total=result["total"],
            list=result["list"]
        )
    }


@router.delete("/delete/{coupon_id}", response_model=CommonResponse)
def delete_coupon(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
) -> Any:
    """删除优惠券"""
    result = coupon_dao.delete(db, coupon_id)
    if not result:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={
                "code": 404,
                "message": "优惠券不存在",
                "data": None
            }
        )
    return {
        "code": 200,
        "message": "删除成功",
        "data": None
    }


@router.post("/coupon-usage-records", response_model=CouponUsageRecordResponse)
def create_usage_record(
        *,
        db: Session = Depends(get_db),
        record_in: CouponUsageRecordCreate,
) -> Any:
    """创建优惠券使用记录"""
    record = coupon_usage_record_dao.create(db, record_in)
    return {
        "code": 200,
        "message": "创建成功",
        "data": record
    }


@router.post("/coupon-usage-records/batch", response_model=CouponUsageRecordBatchResponse)
def batch_create_usage_records(
        *,
        db: Session = Depends(get_db),
        batch_request: CouponUsageRecordBatchCreate,
) -> Any:
    """批量创建优惠券使用记录"""
    try:
        if not batch_request.records:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "批量创建记录列表不能为空",
                    "data": None
                }
            )

        # 按照 coupon_id 和 coupon_batch_id 分组记录
        grouped_records = {}
        for record in batch_request.records:
            key = (record.coupon_id, record.coupon_batch_id)
            if key not in grouped_records:
                grouped_records[key] = []
            grouped_records[key].append(record.user_id)

        # 统计总数
        total_records = len(batch_request.records)
        success_count = 0
        failed_count = 0
        errors = []
        created_records = []

        # 对每个分组调用手动发放服务
        for (coupon_id, coupon_batch_id), user_ids in grouped_records.items():
            try:
                # 调用优惠券手动发放服务
                distribute_result = CouponService.distribute_coupons_manually(
                    session=db,
                    user_ids=user_ids,
                    coupon_id=coupon_id,
                    coupon_batch_id=coupon_batch_id
                )

                if distribute_result["success"]:
                    success_count += distribute_result["distributed_count"]
                    failed_count += distribute_result["failed_count"]

                    # 为成功发放的用户创建记录信息（模拟原有格式）
                    for user_id in user_ids:
                        if user_id not in distribute_result["failed_users"]:
                            created_records.append({
                                "id": None,  # 实际ID由数据库生成
                                "coupon_id": coupon_id,
                                "coupon_batch_id": coupon_batch_id,
                                "user_id": user_id,
                                "order_id": None,
                                "used_at": None,
                                "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                "updated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                "status": "valid"
                            })

                    # 记录失败的用户
                    for failed_user_id in distribute_result["failed_users"]:
                        errors.append({
                            "user_id": failed_user_id,
                            "coupon_id": coupon_id,
                            "coupon_batch_id": coupon_batch_id,
                            "error": "发放失败"
                        })
                else:
                    # 整个分组发放失败
                    failed_count += len(user_ids)
                    for user_id in user_ids:
                        errors.append({
                            "user_id": user_id,
                            "coupon_id": coupon_id,
                            "coupon_batch_id": coupon_batch_id,
                            "error": distribute_result["message"]
                        })

            except Exception as e:
                # 分组发放异常
                failed_count += len(user_ids)
                for user_id in user_ids:
                    errors.append({
                        "user_id": user_id,
                        "coupon_id": coupon_id,
                        "coupon_batch_id": coupon_batch_id,
                        "error": f"发放异常: {str(e)}"
                    })

        # 构造返回结果（保持原有格式）
        result = {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_count": total_records,
            "errors": errors,
            "created_records": created_records
        }

        # 根据结果返回相应的状态码和消息
        if result["failed_count"] == 0:
            message = f"批量创建成功，共创建 {result['success_count']} 条记录"
            code = 200
        elif result["success_count"] == 0:
            message = f"批量创建失败，共 {result['failed_count']} 条记录创建失败"
            code = 400
        else:
            message = f"批量创建部分成功，成功 {result['success_count']} 条，失败 {result['failed_count']} 条"
            code = 200

        return CouponUsageRecordBatchResponse(
            code=code,
            message=message,
            data=result
        )

    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "code": 500,
                "message": f"批量创建优惠券使用记录失败: {str(e)}",
                "data": None
            }
        )


@router.post("/coupon-usage-records/search", response_model=CouponUsageRecordListResponse)
def search_coupon_usage_records(
        *,
        db: Session = Depends(get_db),
        search_params: CouponUsageRecordSearch = Body(...),  # 改为请求体接收
        skip: int = 0,
        limit: int = 100,
) -> Any:
    """搜索优惠券使用记录"""
    result = coupon_usage_record_dao.search_records(
        session=db,
        search_params=search_params,
        skip=skip,
        limit=limit
    )

    # 转换为响应格式
    response_records = []
    for record in result["list"]:
        response_record = CouponUsageRecordResponse(
            id=record.id,
            user_id=record.user_id,
            username=record.user.username,
            coupon_id=record.coupon_id,
            coupon_name=record.coupon.name,
            order_id=record.order_id,
            discount_amount=record.discount_amount,
            distribution_channel=record.distribution_channel.value if record.distribution_channel else None,
            used_at=record.used_at,
            # status=record.status,
            created_at=record.created_at,
            updated_at=record.updated_at
        )
        response_records.append(response_record)

    return CouponUsageRecordListResponse(
        code=200,
        message="查询成功",
        data=response_records
    )


@router.get("/coupon-usage-records/order/{order_id}", response_model=CouponListResponse)
def get_order_usage_records(
        *,
        db: Session = Depends(get_db),
        order_id: int,
) -> Any:
    """获取订单的优惠券使用记录"""
    records = coupon_usage_record_dao.get_by_order(db, order_id)
    return {
        "code": 200,
        "message": "获取成功",
        "data": CouponListData(
            total=len(records),
            list=records
        )
    }


@router.get("/coupon-usage-records/user/{user_id}", response_model=CouponListResponse)
def get_user_usage_records(
        *,
        db: Session = Depends(get_db),
        user_id: int,
) -> Any:
    """获取用户的优惠券使用记录"""
    records = coupon_usage_record_dao.get_by_user(db, user_id)
    return {
        "code": 200,
        "message": "获取成功",
        "data": CouponListData(
            total=len(records),
            list=records
        )
    }


@router.get("/search/name", response_model=CouponNameSearchResponse)
def search_coupons_by_name(
        name: str,
        db: Session = Depends(get_db)
):
    """
    根据优惠券名称进行模糊搜索
    """
    coupons = coupon_dao.search_by_name(db, name)
    return {
        "code": 200,
        "message": "搜索成功",
        "data": coupons
    }


# ====================== 优惠券批次管理接口 ======================

@router.post("/batches/search", response_model=CommonResponse)
def search_coupon_batches(
        *,
        db: Session = Depends(get_db),
        body: dict = Body(...),
) -> Any:
    """搜索优惠券批次"""
    try:
        page = body.get("page", 1)
        page_size = body.get("pageSize", 10)
        keyword = body.get("keyword", None)
        coupon_id = body.get("coupon_id", None)
        status_value = body.get("status", None)

        skip = (page - 1) * page_size

        # 使用简化的数据库查询
        from app.models.coupon import CouponBatch
        query = db.query(CouponBatch)

        # 按关键词搜索（批次名称）
        if keyword:
            query = query.filter(CouponBatch.name.contains(keyword))

        # 按优惠券ID筛选
        if coupon_id:
            query = query.filter(CouponBatch.coupon_id == coupon_id)

        # 按状态筛选
        if status_value is not None:
            from app.models.enum import Status
            if status_value == 1:
                query = query.filter(CouponBatch.status == Status.ACTIVE)
            else:
                query = query.filter(CouponBatch.status == Status.INACTIVE)

        # 获取总数
        total = query.count()

        # 分页查询
        batches = query.offset(skip).limit(page_size).all()

        # 手动序列化
        batch_list = []
        for batch in batches:
            batch_dict = serialize_coupon_batch(batch)
            if batch_dict:
                batch_list.append(batch_dict)

        return {
            "code": 200,
            "message": "搜索成功",
            "data": {
                "total": total,
                "list": batch_list
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"搜索优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.get("/batches/view/{batch_id}", response_model=CommonResponse)
def get_coupon_batch(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
) -> Any:
    """获取优惠券批次详情"""
    try:
        batch = coupon_batch_dao.get(db, batch_id)
        if not batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "获取优惠券批次详情成功",
            "data": {
                "id": batch.id,
                "name": batch.name,
                "description": batch.description,
                "batch_number": batch.batch_number,
                "quantity": batch.quantity,
                "start_time": batch.start_time.isoformat() if batch.start_time else None,
                "end_time": batch.end_time.isoformat() if batch.end_time else None,
                "valid_duration": batch.valid_duration,
                "coupon_id": batch.coupon_id,
                "status": batch.status.value,
                "distribution_channels": batch.distribution_channels,
                "distribution_quantity": batch.distribution_quantity,
                "distribution_cycle": batch.distribution_cycle.value if batch.distribution_cycle else None,
                "receive_quantity": batch.receive_quantity,
                "receive_cycle": batch.receive_cycle.value if batch.receive_cycle else None,
                "receive_start_time": batch.receive_start_time.isoformat() if batch.receive_start_time else None,
                "receive_end_time": batch.receive_end_time.isoformat() if batch.receive_end_time else None,
                "created_at": batch.created_at.isoformat() if batch.created_at else None,
                "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取优惠券批次详情失败: {str(e)}",
                "data": None
            }
        )


@router.post("/batches/create", response_model=CommonResponse)
def create_coupon_batch(
        *,
        db: Session = Depends(get_db),
        body: dict = Body(...),
) -> Any:
    """创建优惠券批次"""
    try:
        # 验证必需字段
        required_fields = ["name", "coupon_id", "quantity", "start_time", "end_time"]
        for field in required_fields:
            if field not in body:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "code": 400,
                        "message": f"缺少必需字段: {field}",
                        "data": None
                    }
                )

        # 验证优惠券是否存在
        coupon = coupon_dao.get(db, body["coupon_id"])
        if not coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "关联的优惠券不存在",
                    "data": None
                }
            )

        # 使用真实的数据库操作
        from app.models.coupon import CouponBatch
        from app.models.enum import Status
        from datetime import datetime

        # 创建批次对象
        batch_data = {
            "name": body.get("name"),
            "description": body.get("description", ""),
            "batch_number": body.get("batch_number"),
            "quantity": body.get("quantity"),
            "coupon_id": body.get("coupon_id"),
            "valid_duration": body.get("valid_duration", 0),
            "status": Status.ACTIVE if body.get("status", 1) == 1 else Status.INACTIVE,

            # 发放相关字段
            "distribution_channels": body.get("distribution_channels", []),
            "distribution_quantity": body.get("distribution_quantity", 0),
            "distribution_cycle": body.get("distribution_cycle", "per_day"),

            # 领取相关字段
            "receive_quantity": body.get("receive_quantity", 0),
            "receive_cycle": body.get("receive_cycle", "per_day"),
        }

        # 处理时间字段
        if body.get("start_time"):
            batch_data["start_time"] = datetime.fromisoformat(body.get("start_time").replace('Z', '+00:00'))
        if body.get("end_time"):
            batch_data["end_time"] = datetime.fromisoformat(body.get("end_time").replace('Z', '+00:00'))
        if body.get("receive_start_time"):
            batch_data["receive_start_time"] = datetime.fromisoformat(body.get("receive_start_time").replace('Z', '+00:00'))
        if body.get("receive_end_time"):
            batch_data["receive_end_time"] = datetime.fromisoformat(body.get("receive_end_time").replace('Z', '+00:00'))

        # 处理枚举字段
        from app.models.coupon import CouponUsageCycle
        if body.get("distribution_cycle"):
            batch_data["distribution_cycle"] = CouponUsageCycle(body.get("distribution_cycle"))
        if body.get("receive_cycle"):
            batch_data["receive_cycle"] = CouponUsageCycle(body.get("receive_cycle"))

        batch = CouponBatch(**batch_data)
        db.add(batch)
        db.commit()
        db.refresh(batch)

        # 手动序列化返回结果
        result_data = serialize_coupon_batch_detail(batch)

        return {
            "code": 200,
            "message": "创建优惠券批次成功",
            "data": result_data
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"创建优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.put("/batches/update/{batch_id}", response_model=CommonResponse)
def update_coupon_batch(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
        body: dict = Body(...),
) -> Any:
    """更新优惠券批次"""
    try:
        # 检查批次是否存在
        existing_batch = coupon_batch_dao.get(db, batch_id)
        if not existing_batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        # 如果更新了coupon_id，验证优惠券是否存在
        if "coupon_id" in body:
            coupon = coupon_dao.get(db, body["coupon_id"])
            if not coupon:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail={
                        "code": 404,
                        "message": "关联的优惠券不存在",
                        "data": None
                    }
                )

        # 处理时间字段和枚举字段
        update_data = dict(body)

        # 处理时间字段
        time_fields = ['start_time', 'end_time', 'receive_start_time', 'receive_end_time']
        for field in time_fields:
            if field in update_data and update_data[field]:
                try:
                    update_data[field] = datetime.fromisoformat(update_data[field].replace('Z', '+00:00'))
                except ValueError:
                    # 如果解析失败，保持原值
                    pass

        # 处理枚举字段
        from app.models.coupon import CouponUsageCycle, Status
        if 'distribution_cycle' in update_data:
            update_data['distribution_cycle'] = CouponUsageCycle(update_data['distribution_cycle'])
        if 'receive_cycle' in update_data:
            update_data['receive_cycle'] = CouponUsageCycle(update_data['receive_cycle'])
        if 'status' in update_data:
            update_data['status'] = Status.ACTIVE if update_data['status'] == 1 else Status.INACTIVE

        # 更新批次
        updated_batch = coupon_batch_dao.update(db, batch_id, **update_data)
        if not updated_batch:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "更新优惠券批次失败",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "更新优惠券批次成功",
            "data": {
                "id": updated_batch.id,
                "name": updated_batch.name,
                "description": updated_batch.description,
                "batch_number": updated_batch.batch_number,
                "quantity": updated_batch.quantity,
                "coupon_id": updated_batch.coupon_id,
                "status": updated_batch.status.value,
                "start_time": updated_batch.start_time.isoformat() if updated_batch.start_time else None,
                "end_time": updated_batch.end_time.isoformat() if updated_batch.end_time else None,
                "valid_duration": updated_batch.valid_duration
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.delete("/batches/delete/{batch_id}", response_model=CommonResponse)
def delete_coupon_batch(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
) -> Any:
    """删除优惠券批次"""
    try:
        result = coupon_batch_dao.delete(db, batch_id)
        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "删除优惠券批次成功",
            "data": None
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"删除优惠券批次失败: {str(e)}",
                "data": None
            }
        )


@router.post("/batches/status/{batch_id}", response_model=CommonResponse)
def update_coupon_batch_status(
        *,
        db: Session = Depends(get_db),
        batch_id: int,
        body: dict = Body(...),
) -> Any:
    """更新优惠券批次状态"""
    try:
        status_value = body.get("status")
        if status_value is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "code": 400,
                    "message": "缺少状态参数",
                    "data": None
                }
            )

        batch = coupon_batch_dao.update_status(db, batch_id, status_value)
        if not batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券批次不存在",
                    "data": None
                }
            )

        return {
            "code": 200,
            "message": "优惠券批次状态更新成功",
            "data": {
                "id": batch.id,
                "name": batch.name,
                "status": batch.status.value
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"更新优惠券批次状态失败: {str(e)}",
                "data": None
            }
        )


@router.get("/batches/coupon/{coupon_id}", response_model=CommonResponse)
def get_coupon_batches_by_coupon_id(
        *,
        db: Session = Depends(get_db),
        coupon_id: int,
) -> Any:
    """根据优惠券ID获取批次列表"""
    try:
        # 验证优惠券是否存在
        coupon = coupon_dao.get(db, coupon_id)
        if not coupon:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "code": 404,
                    "message": "优惠券不存在",
                    "data": None
                }
            )

        batches = coupon_batch_dao.get_by_coupon_id(db, coupon_id)

        # 转换为字典格式
        batch_list = []
        for batch in batches:
            batch_dict = {
                "id": batch.id,
                "name": batch.name,
                "description": batch.description,
                "batch_number": batch.batch_number,
                "quantity": batch.quantity,
                "start_time": batch.start_time.isoformat() if batch.start_time else None,
                "end_time": batch.end_time.isoformat() if batch.end_time else None,
                "valid_duration": batch.valid_duration,
                "coupon_id": batch.coupon_id,
                "status": batch.status.value,
                "created_at": batch.created_at.isoformat() if batch.created_at else None,
                "updated_at": batch.updated_at.isoformat() if batch.updated_at else None
            }
            batch_list.append(batch_dict)

        return {
            "code": 200,
            "message": "获取优惠券批次列表成功",
            "data": {
                "total": len(batch_list),
                "list": batch_list
            }
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "code": 400,
                "message": f"获取优惠券批次列表失败: {str(e)}",
                "data": None
            }
        )
